module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/components/BreederChart.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts/highcharts.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts-react-official/dist/highcharts-react.min.js [app-ssr] (ecmascript)");
'use client';
;
;
;
// Dinamikus import az export modulokhoz
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
const BreederChart = ({ title, varieties, breederColor, breederName })=>{
    // Színárnyalatok generálása a fajtákhoz
    const generateColorShades = (baseColor, count)=>{
        const colors = [];
        for(let i = 0; i < count; i++){
            const factor = 0.3 + i * 0.7 / Math.max(count - 1, 1);
            colors.push(adjustColorBrightness(baseColor, factor));
        }
        return colors;
    };
    const adjustColorBrightness = (hex, factor)=>{
        const num = parseInt(hex.replace('#', ''), 16);
        const R = Math.round((num >> 16) * factor);
        const G = Math.round((num >> 8 & 0x00FF) * factor);
        const B = Math.round((num & 0x0000FF) * factor);
        return '#' + (R << 16 | G << 8 | B).toString(16).padStart(6, '0');
    };
    const colors = generateColorShades(breederColor, varieties.length);
    // Adatok előkészítése Highcharts számára
    const categories = [
        'M-I',
        'M-II',
        'Cs-I',
        'Cs-II',
        'L-I',
        'L-II'
    ];
    const series = varieties.map((variety, index)=>({
            type: 'column',
            name: variety.variety,
            data: categories.map((location)=>variety.locations[location]),
            color: colors[index]
        }));
    const options = {
        chart: {
            type: 'column',
            backgroundColor: 'transparent',
            style: {
                fontFamily: 'var(--font-geist-sans)'
            }
        },
        title: {
            text: `${breederName}`,
            style: {
                color: '#ffffff',
                fontSize: '18px',
                fontWeight: '600'
            }
        },
        subtitle: {
            text: title,
            style: {
                color: '#a1a1aa',
                fontSize: '14px'
            }
        },
        xAxis: {
            categories: categories,
            labels: {
                style: {
                    color: '#a1a1aa'
                }
            },
            lineColor: '#3f3f46',
            tickColor: '#3f3f46',
            plotBands: [
                {
                    from: -0.5,
                    to: 1.5,
                    color: 'rgba(255, 255, 255, 0.02)',
                    label: {
                        text: 'Mezőberény',
                        style: {
                            color: '#6b7280',
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                },
                {
                    from: 1.5,
                    to: 3.5,
                    color: 'rgba(255, 255, 255, 0.05)',
                    label: {
                        text: 'Csabacsűd',
                        style: {
                            color: '#6b7280',
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                },
                {
                    from: 3.5,
                    to: 5.5,
                    color: 'rgba(255, 255, 255, 0.02)',
                    label: {
                        text: 'Lakitelek',
                        style: {
                            color: '#6b7280',
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                }
            ]
        },
        yAxis: {
            title: {
                text: 't/ha',
                style: {
                    color: '#a1a1aa'
                }
            },
            labels: {
                style: {
                    color: '#a1a1aa'
                }
            },
            gridLineColor: '#3f3f46'
        },
        legend: {
            enabled: true,
            itemStyle: {
                color: '#a1a1aa'
            },
            itemHoverStyle: {
                color: '#ffffff'
            }
        },
        plotOptions: {
            column: {
                borderWidth: 0,
                borderRadius: 3,
                groupPadding: 0.1,
                pointPadding: 0.05,
                dataLabels: {
                    enabled: false
                }
            }
        },
        series: series,
        credits: {
            enabled: false
        },
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    symbolStroke: '#ffffff',
                    theme: {
                        fill: '#4b5563',
                        stroke: '#6b7280'
                    },
                    menuItems: [
                        'viewFullscreen',
                        'separator',
                        'downloadPNG',
                        'downloadJPEG',
                        'downloadPDF',
                        'downloadSVG',
                        'separator',
                        'downloadCSV',
                        'downloadXLS'
                    ]
                }
            }
        },
        tooltip: {
            backgroundColor: '#27272a',
            borderColor: '#3f3f46',
            borderRadius: 8,
            style: {
                color: '#ffffff'
            },
            useHTML: true,
            shared: true,
            formatter: function() {
                const currentLocation = this.x;
                let tooltipContent = `<div style="padding: 12px; min-width: 200px;">
          <div style="font-weight: bold; margin-bottom: 12px; color: #ffffff; font-size: 14px;">
            ${currentLocation} - Összes fajta
          </div>`;
                // Az aktuális helyszínen lévő összes fajta értékét megjelenítjük
                this.points?.forEach((point)=>{
                    const varietyName = point.series.name;
                    const value = point.y;
                    const color = point.series.color;
                    tooltipContent += `<div style="margin: 6px 0; display: flex; align-items: center;">
            <div style="width: 12px; height: 12px; background-color: ${color}; border-radius: 2px; margin-right: 8px;"></div>
            <span style="color: #ffffff; font-weight: 500;">${varietyName}:</span>
            <span style="margin-left: 8px; color: #fbbf24; font-weight: bold;">${value} t/ha</span>
          </div>`;
                });
                tooltipContent += '</div>';
                return tooltipContent;
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-96",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            highcharts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
            options: options
        }, void 0, false, {
            fileName: "[project]/src/components/BreederChart.tsx",
            lineNumber: 232,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/BreederChart.tsx",
        lineNumber: 231,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = BreederChart;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__0c517051._.js.map