{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ColumnChart.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Highcharts from 'highcharts';\nimport HighchartsReact from 'highcharts-react-official';\n\ninterface ColumnChartProps {\n  title?: string;\n  data?: Array<{ name: string; y: number }>;\n}\n\nconst ColumnChart: React.FC<ColumnChartProps> = ({ \n  title = \"Oszlop Diagram\", \n  data = [\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 29.9 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 71.5 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 106.4 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 129.2 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 144.0 },\n    { name: '<PERSON><PERSON><PERSON>', y: 176.0 }\n  ]\n}) => {\n  const options: Highcharts.Options = {\n    chart: {\n      type: 'column',\n      backgroundColor: 'transparent',\n      style: {\n        fontFamily: 'var(--font-geist-sans)'\n      }\n    },\n    title: {\n      text: title,\n      style: {\n        color: '#ffffff',\n        fontSize: '20px',\n        fontWeight: '600'\n      }\n    },\n    xAxis: {\n      type: 'category',\n      labels: {\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      lineColor: '#3f3f46',\n      tickColor: '#3f3f46'\n    },\n    yAxis: {\n      title: {\n        text: 'Értékek',\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      labels: {\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      gridLineColor: '#3f3f46'\n    },\n    legend: {\n      enabled: false\n    },\n    plotOptions: {\n      column: {\n        borderWidth: 0,\n        borderRadius: 4,\n        color: {\n          linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\n          stops: [\n            [0, '#8b5cf6'],\n            [1, '#6366f1']\n          ]\n        },\n        dataLabels: {\n          enabled: true,\n          style: {\n            color: '#ffffff',\n            textOutline: 'none'\n          }\n        }\n      }\n    },\n    series: [{\n      type: 'column',\n      name: 'Adatok',\n      data: data\n    }],\n    credits: {\n      enabled: false\n    },\n    tooltip: {\n      backgroundColor: '#27272a',\n      borderColor: '#3f3f46',\n      style: {\n        color: '#ffffff'\n      }\n    }\n  };\n\n  return (\n    <div className=\"w-full h-96\">\n      <HighchartsReact\n        highcharts={Highcharts}\n        options={options}\n      />\n    </div>\n  );\n};\n\nexport default ColumnChart;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAWA,MAAM,cAA0C,CAAC,EAC/C,QAAQ,gBAAgB,EACxB,OAAO;IACL;QAAE,MAAM;QAAU,GAAG;IAAK;IAC1B;QAAE,MAAM;QAAW,GAAG;IAAK;IAC3B;QAAE,MAAM;QAAW,GAAG;IAAM;IAC5B;QAAE,MAAM;QAAW,GAAG;IAAM;IAC5B;QAAE,MAAM;QAAS,GAAG;IAAM;IAC1B;QAAE,MAAM;QAAU,GAAG;IAAM;CAC5B,EACF;IACC,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,OAAO;gBACL,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM;YACN,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,eAAe;QACjB;QACA,QAAQ;YACN,SAAS;QACX;QACA,aAAa;YACX,QAAQ;gBACN,aAAa;gBACb,cAAc;gBACd,OAAO;oBACL,gBAAgB;wBAAE,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;oBAAE;oBAC7C,OAAO;wBACL;4BAAC;4BAAG;yBAAU;wBACd;4BAAC;4BAAG;yBAAU;qBACf;gBACH;gBACA,YAAY;oBACV,SAAS;oBACT,OAAO;wBACL,OAAO;wBACP,aAAa;oBACf;gBACF;YACF;QACF;QACA,QAAQ;YAAC;gBACP,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SAAE;QACF,SAAS;YACP,SAAS;QACX;QACA,SAAS;YACP,iBAAiB;YACjB,aAAa;YACb,OAAO;gBACL,OAAO;YACT;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gMAAe;YACd,YAAY,mJAAU;YACtB,SAAS;;;;;;;;;;;AAIjB;uCAEe", "debugId": null}}]}