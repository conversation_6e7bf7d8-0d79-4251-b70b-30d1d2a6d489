import ColumnChart from "@/components/ColumnChart";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-4xl font-bold text-foreground">
            Univer 2025 Dashboard
          </h1>
          <p className="text-muted-foreground text-lg">
            Modern adatvizualizáció Highcharts-szal
          </p>
        </div>

        {/* Chart Card */}
        <Card className="w-full">
          <CardHeader>
            <CardTitle className="text-2xl">Havi <PERSON></CardTitle>
            <CardDescription>
              Oszlop diagram a havi értékek megjelenítésére
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ColumnC<PERSON>
              title="Havi <PERSON>"
              data={[
                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 29.9 },
                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 71.5 },
                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 106.4 },
                { name: '<PERSON>p<PERSON><PERSON>', y: 129.2 },
                { name: '<PERSON><PERSON>jus', y: 144.0 },
                { name: '<PERSON><PERSON><PERSON>', y: 176.0 },
                { name: '<PERSON><PERSON><PERSON>', y: 135.6 },
                { name: 'Augusztus', y: 148.5 }
              ]}
            />
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Összes Érték
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">841.1</div>
              <p className="text-xs text-muted-foreground">
                +12.5% az előző hónaphoz képest
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Átlag
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">105.1</div>
              <p className="text-xs text-muted-foreground">
                Havi átlagérték
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                Legmagasabb
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">176.0</div>
              <p className="text-xs text-muted-foreground">
                Június hónapban
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
