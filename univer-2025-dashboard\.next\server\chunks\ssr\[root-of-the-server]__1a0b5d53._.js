module.exports = [
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[project]/src/components/ColumnChart.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts/highcharts.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts-react-official/dist/highcharts-react.min.js [app-ssr] (ecmascript)");
'use client';
;
;
;
const ColumnChart = ({ title = "Oszlop Diagram", data = [
    {
        name: 'Január',
        y: 29.9
    },
    {
        name: 'Február',
        y: 71.5
    },
    {
        name: 'Március',
        y: 106.4
    },
    {
        name: 'Április',
        y: 129.2
    },
    {
        name: 'Május',
        y: 144.0
    },
    {
        name: 'Június',
        y: 176.0
    }
] })=>{
    const options = {
        chart: {
            type: 'column',
            backgroundColor: 'transparent',
            style: {
                fontFamily: 'var(--font-geist-sans)'
            }
        },
        title: {
            text: title,
            style: {
                color: '#ffffff',
                fontSize: '20px',
                fontWeight: '600'
            }
        },
        xAxis: {
            type: 'category',
            labels: {
                style: {
                    color: '#a1a1aa'
                }
            },
            lineColor: '#3f3f46',
            tickColor: '#3f3f46'
        },
        yAxis: {
            title: {
                text: 'Értékek',
                style: {
                    color: '#a1a1aa'
                }
            },
            labels: {
                style: {
                    color: '#a1a1aa'
                }
            },
            gridLineColor: '#3f3f46'
        },
        legend: {
            enabled: false
        },
        plotOptions: {
            column: {
                borderWidth: 0,
                borderRadius: 4,
                color: {
                    linearGradient: {
                        x1: 0,
                        y1: 0,
                        x2: 0,
                        y2: 1
                    },
                    stops: [
                        [
                            0,
                            '#8b5cf6'
                        ],
                        [
                            1,
                            '#6366f1'
                        ]
                    ]
                },
                dataLabels: {
                    enabled: true,
                    style: {
                        color: '#ffffff',
                        textOutline: 'none'
                    }
                }
            }
        },
        series: [
            {
                type: 'column',
                name: 'Adatok',
                data: data
            }
        ],
        credits: {
            enabled: false
        },
        tooltip: {
            backgroundColor: '#27272a',
            borderColor: '#3f3f46',
            style: {
                color: '#ffffff'
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-96",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            highcharts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
            options: options
        }, void 0, false, {
            fileName: "[project]/src/components/ColumnChart.tsx",
            lineNumber: 105,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/ColumnChart.tsx",
        lineNumber: 104,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = ColumnChart;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__1a0b5d53._.js.map