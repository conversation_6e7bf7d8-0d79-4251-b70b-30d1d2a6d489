'use client';

import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { ProcessedData } from '@/utils/dataProcessor';

interface BreederChartProps {
  title: string;
  varieties: ProcessedData[];
  breederColor: string;
  breederName: string;
}

const BreederChart: React.FC<BreederChartProps> = ({ 
  title, 
  varieties, 
  breederColor,
  breederName 
}) => {
  // Színárnyalatok generálása a fajtákhoz
  const generateColorShades = (baseColor: string, count: number): string[] => {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);
      colors.push(adjustColorBrightness(baseColor, factor));
    }
    return colors;
  };

  const adjustColorBrightness = (hex: string, factor: number): string => {
    const num = parseInt(hex.replace('#', ''), 16);
    const R = Math.round((num >> 16) * factor);
    const G = Math.round(((num >> 8) & 0x00FF) * factor);
    const B = Math.round((num & 0x0000FF) * factor);
    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');
  };

  const colors = generateColorShades(breederColor, varieties.length);

  // Adatok előkészítése Highcharts számára
  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];
  
  const series = varieties.map((variety, index) => ({
    name: variety.variety,
    data: categories.map(location => 
      variety.locations[location as keyof typeof variety.locations]
    ),
    color: colors[index]
  }));

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'var(--font-geist-sans)'
      }
    },
    title: {
      text: `${breederName}`,
      style: {
        color: '#ffffff',
        fontSize: '18px',
        fontWeight: '600'
      }
    },
    subtitle: {
      text: title,
      style: {
        color: '#a1a1aa',
        fontSize: '14px'
      }
    },
    xAxis: {
      categories: categories,
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      lineColor: '#3f3f46',
      tickColor: '#3f3f46',
      plotBands: [
        {
          from: -0.5,
          to: 1.5,
          color: 'rgba(255, 255, 255, 0.02)',
          label: {
            text: 'Mezőberény',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 1.5,
          to: 3.5,
          color: 'rgba(255, 255, 255, 0.05)',
          label: {
            text: 'Csabacsűd',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 3.5,
          to: 5.5,
          color: 'rgba(255, 255, 255, 0.02)',
          label: {
            text: 'Lakitelek',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        }
      ]
    },
    yAxis: {
      title: {
        text: 't/ha',
        style: {
          color: '#a1a1aa'
        }
      },
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      gridLineColor: '#3f3f46'
    },
    legend: {
      enabled: true,
      itemStyle: {
        color: '#a1a1aa'
      },
      itemHoverStyle: {
        color: '#ffffff'
      }
    },
    plotOptions: {
      column: {
        borderWidth: 0,
        borderRadius: 3,
        groupPadding: 0.1,
        pointPadding: 0.05,
        dataLabels: {
          enabled: false
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: '#27272a',
      borderColor: '#3f3f46',
      style: {
        color: '#ffffff'
      },
      formatter: function() {
        return `<b>${this.series.name}</b><br/>
                ${this.x}: <b>${this.y} t/ha</b>`;
      }
    }
  };

  return (
    <div className="w-full h-96">
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
      />
    </div>
  );
};

export default BreederChart;
