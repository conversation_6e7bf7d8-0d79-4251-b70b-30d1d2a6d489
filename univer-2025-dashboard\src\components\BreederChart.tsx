'use client';

import React, { useEffect } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { ProcessedData } from '@/utils/dataProcessor';

interface BreederChartProps {
  title: string;
  varieties: ProcessedData[];
  breederColor: string;
  breederName: string;
  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez
}

const BreederChart: React.FC<BreederChartProps> = ({
  title,
  varieties,
  breederColor,
  breederName,
  allVarietiesData = []
}) => {
  // Színárnyalatok generálása a fajtákhoz
  const generateColorShades = (baseColor: string, count: number): string[] => {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);
      colors.push(adjustColorBrightness(baseColor, factor));
    }
    return colors;
  };

  const adjustColorBrightness = (hex: string, factor: number): string => {
    const num = parseInt(hex.replace('#', ''), 16);
    const R = Math.round((num >> 16) * factor);
    const G = Math.round(((num >> 8) & 0x00FF) * factor);
    const B = Math.round((num & 0x0000FF) * factor);
    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');
  };

  const colors = generateColorShades(breederColor, varieties.length);

  // Adatok előkészítése Highcharts számára
  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];
  
  const series = varieties.map((variety, index) => ({
    type: 'column' as const,
    name: variety.variety,
    data: categories.map(location =>
      variety.locations[location as keyof typeof variety.locations]
    ),
    color: colors[index]
  }));

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'var(--font-geist-sans)'
      }
    },
    title: {
      text: `${breederName}`,
      style: {
        color: '#ffffff',
        fontSize: '18px',
        fontWeight: '600'
      }
    },
    subtitle: {
      text: title,
      style: {
        color: '#a1a1aa',
        fontSize: '14px'
      }
    },
    xAxis: {
      categories: categories,
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      lineColor: '#3f3f46',
      tickColor: '#3f3f46',
      plotBands: [
        {
          from: -0.5,
          to: 1.5,
          color: 'rgba(255, 255, 255, 0.02)',
          label: {
            text: 'Mezőberény',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 1.5,
          to: 3.5,
          color: 'rgba(255, 255, 255, 0.05)',
          label: {
            text: 'Csabacsűd',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 3.5,
          to: 5.5,
          color: 'rgba(255, 255, 255, 0.02)',
          label: {
            text: 'Lakitelek',
            style: {
              color: '#6b7280',
              fontSize: '12px'
            },
            align: 'center'
          }
        }
      ]
    },
    yAxis: {
      title: {
        text: 't/ha',
        style: {
          color: '#a1a1aa'
        }
      },
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      gridLineColor: '#3f3f46'
    },
    legend: {
      enabled: true,
      itemStyle: {
        color: '#a1a1aa'
      },
      itemHoverStyle: {
        color: '#ffffff'
      }
    },
    plotOptions: {
      column: {
        borderWidth: 0,
        borderRadius: 3,
        groupPadding: 0.1,
        pointPadding: 0.05,
        dataLabels: {
          enabled: false
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          theme: {
            fill: '#3f3f46',
            stroke: '#6b7280'
          },
          menuItems: [
            'viewFullscreen',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadPDF',
            'downloadSVG',
            'separator',
            'downloadCSV',
            'downloadXLS'
          ]
        }
      }
    },
    tooltip: {
      backgroundColor: '#27272a',
      borderColor: '#3f3f46',
      borderRadius: 8,
      style: {
        color: '#ffffff'
      },
      useHTML: true,
      formatter: function() {
        const varietyName = this.series.name;
        const currentLocation = this.x;
        const currentValue = this.y;

        // Megkeressük az adott fajta összes előfordulását
        const varietyData = allVarietiesData.find(v => v.variety === varietyName);

        if (!varietyData) {
          return `<b>${varietyName}</b><br/>
                  ${currentLocation}: <b>${currentValue} t/ha</b>`;
        }

        let tooltipContent = `<div style="padding: 8px;">
          <div style="font-weight: bold; margin-bottom: 8px; color: #ffffff;">${varietyName}</div>
          <div style="margin-bottom: 4px;"><strong>${currentLocation}: ${currentValue} t/ha</strong></div>
          <hr style="border: 1px solid #4b5563; margin: 8px 0;">
          <div style="font-size: 12px; color: #d1d5db;">Összes helyszín:</div>`;

        const locations = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];
        locations.forEach(location => {
          const value = varietyData.locations[location as keyof typeof varietyData.locations];
          const isCurrentLocation = location === String(currentLocation);
          tooltipContent += `<div style="margin: 2px 0; ${isCurrentLocation ? 'font-weight: bold; color: #fbbf24;' : 'color: #9ca3af;'}">
            ${location}: ${value} t/ha
          </div>`;
        });

        tooltipContent += '</div>';
        return tooltipContent;
      }
    }
  };

  return (
    <div className="w-full h-96">
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
      />
    </div>
  );
};

export default BreederChart;
