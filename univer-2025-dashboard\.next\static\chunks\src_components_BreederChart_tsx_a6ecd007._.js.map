{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/BreederChart.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport Highcharts from 'highcharts';\nimport HighchartsReact from 'highcharts-react-official';\nimport { ProcessedData } from '@/utils/dataProcessor';\n\ninterface BreederChartProps {\n  title: string;\n  varieties: ProcessedData[];\n  breederColor: string;\n  breederName: string;\n  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez\n}\n\nconst BreederChart: React.FC<BreederChartProps> = ({\n  title,\n  varieties,\n  breederColor,\n  breederName,\n  allVarietiesData = []\n}) => {\n  // Export modulok betöltése\n  useEffect(() => {\n    if (typeof window !== 'undefined') {\n      try {\n        require('highcharts/modules/exporting')(Highcharts);\n        require('highcharts/modules/export-data')(Highcharts);\n      } catch (error) {\n        console.warn('Highcharts export modules could not be loaded:', error);\n      }\n    }\n  }, []);\n  // Színárnyalatok generálása a fajtákhoz\n  const generateColorShades = (baseColor: string, count: number): string[] => {\n    const colors: string[] = [];\n    for (let i = 0; i < count; i++) {\n      const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);\n      colors.push(adjustColorBrightness(baseColor, factor));\n    }\n    return colors;\n  };\n\n  const adjustColorBrightness = (hex: string, factor: number): string => {\n    const num = parseInt(hex.replace('#', ''), 16);\n    const R = Math.round((num >> 16) * factor);\n    const G = Math.round(((num >> 8) & 0x00FF) * factor);\n    const B = Math.round((num & 0x0000FF) * factor);\n    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');\n  };\n\n  const colors = generateColorShades(breederColor, varieties.length);\n\n  // Adatok előkészítése Highcharts számára\n  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\n  \n  const series = varieties.map((variety, index) => ({\n    type: 'column' as const,\n    name: variety.variety,\n    data: categories.map(location =>\n      variety.locations[location as keyof typeof variety.locations]\n    ),\n    color: colors[index]\n  }));\n\n  const options: Highcharts.Options = {\n    chart: {\n      type: 'column',\n      backgroundColor: 'transparent',\n      style: {\n        fontFamily: 'var(--font-geist-sans)'\n      }\n    },\n    title: {\n      text: `${breederName}`,\n      style: {\n        color: '#ffffff',\n        fontSize: '18px',\n        fontWeight: '600'\n      }\n    },\n    subtitle: {\n      text: title,\n      style: {\n        color: '#a1a1aa',\n        fontSize: '14px'\n      }\n    },\n    xAxis: {\n      categories: categories,\n      labels: {\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      lineColor: '#3f3f46',\n      tickColor: '#3f3f46',\n      plotBands: [\n        {\n          from: -0.5,\n          to: 1.5,\n          color: 'rgba(255, 255, 255, 0.02)',\n          label: {\n            text: 'Mezőberény',\n            style: {\n              color: '#6b7280',\n              fontSize: '12px'\n            },\n            align: 'center'\n          }\n        },\n        {\n          from: 1.5,\n          to: 3.5,\n          color: 'rgba(255, 255, 255, 0.05)',\n          label: {\n            text: 'Csabacsűd',\n            style: {\n              color: '#6b7280',\n              fontSize: '12px'\n            },\n            align: 'center'\n          }\n        },\n        {\n          from: 3.5,\n          to: 5.5,\n          color: 'rgba(255, 255, 255, 0.02)',\n          label: {\n            text: 'Lakitelek',\n            style: {\n              color: '#6b7280',\n              fontSize: '12px'\n            },\n            align: 'center'\n          }\n        }\n      ]\n    },\n    yAxis: {\n      title: {\n        text: 't/ha',\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      labels: {\n        style: {\n          color: '#a1a1aa'\n        }\n      },\n      gridLineColor: '#3f3f46'\n    },\n    legend: {\n      enabled: true,\n      itemStyle: {\n        color: '#a1a1aa'\n      },\n      itemHoverStyle: {\n        color: '#ffffff'\n      }\n    },\n    plotOptions: {\n      column: {\n        borderWidth: 0,\n        borderRadius: 3,\n        groupPadding: 0.1,\n        pointPadding: 0.05,\n        dataLabels: {\n          enabled: false\n        }\n      }\n    },\n    series: series,\n    credits: {\n      enabled: false\n    },\n    exporting: {\n      enabled: true,\n      buttons: {\n        contextButton: {\n          symbolStroke: '#ffffff',\n          theme: {\n            fill: '#4b5563',\n            stroke: '#6b7280'\n          }\n        }\n      }\n    },\n    tooltip: {\n      backgroundColor: '#27272a',\n      borderColor: '#3f3f46',\n      borderRadius: 8,\n      style: {\n        color: '#ffffff'\n      },\n      useHTML: true,\n      formatter: function() {\n        const varietyName = this.series.name;\n        const currentLocation = this.x;\n        const currentValue = this.y;\n\n        // Megkeressük az adott fajta összes előfordulását\n        const varietyData = allVarietiesData.find(v => v.variety === varietyName);\n\n        if (!varietyData) {\n          return `<b>${varietyName}</b><br/>\n                  ${currentLocation}: <b>${currentValue} t/ha</b>`;\n        }\n\n        let tooltipContent = `<div style=\"padding: 8px;\">\n          <div style=\"font-weight: bold; margin-bottom: 8px; color: #ffffff;\">${varietyName}</div>\n          <div style=\"margin-bottom: 4px;\"><strong>${currentLocation}: ${currentValue} t/ha</strong></div>\n          <hr style=\"border: 1px solid #4b5563; margin: 8px 0;\">\n          <div style=\"font-size: 12px; color: #d1d5db;\">Összes helyszín:</div>`;\n\n        const locations = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\n        locations.forEach(location => {\n          const value = varietyData.locations[location as keyof typeof varietyData.locations];\n          const isCurrentLocation = location === String(currentLocation);\n          tooltipContent += `<div style=\"margin: 2px 0; ${isCurrentLocation ? 'font-weight: bold; color: #fbbf24;' : 'color: #9ca3af;'}\">\n            ${location}: ${value} t/ha\n          </div>`;\n        });\n\n        tooltipContent += '</div>';\n        return tooltipContent;\n      }\n    }\n  };\n\n  return (\n    <div className=\"w-full h-96\">\n      <HighchartsReact\n        highcharts={Highcharts}\n        options={options}\n      />\n    </div>\n  );\n};\n\nexport default BreederChart;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAeA,MAAM,eAA4C;QAAC,EACjD,KAAK,EACL,SAAS,EACT,YAAY,EACZ,WAAW,EACX,mBAAmB,EAAE,EACtB;;IACC,2BAA2B;IAC3B,IAAA,0KAAS;kCAAC;YACR,wCAAmC;gBACjC,IAAI;oBACF,4GAAwC,sJAAU;oBAClD,8GAA0C,sJAAU;gBACtD,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,kDAAkD;gBACjE;YACF;QACF;iCAAG,EAAE;IACL,wCAAwC;IACxC,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,SAAS,MAAM,AAAC,IAAI,MAAO,KAAK,GAAG,CAAC,QAAQ,GAAG;YACrD,OAAO,IAAI,CAAC,sBAAsB,WAAW;QAC/C;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI;QACnC,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,AAAC,OAAO,IAAK,MAAM,IAAI;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,QAAQ,IAAI;QACxC,OAAO,MAAM,CAAC,AAAC,KAAK,KAAO,KAAK,IAAK,CAAC,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;IACnE;IAEA,MAAM,SAAS,oBAAoB,cAAc,UAAU,MAAM;IAEjE,yCAAyC;IACzC,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAElE,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;YAChD,MAAM;YACN,MAAM,QAAQ,OAAO;YACrB,MAAM,WAAW,GAAG,CAAC,CAAA,WACnB,QAAQ,SAAS,CAAC,SAA2C;YAE/D,OAAO,MAAM,CAAC,MAAM;QACtB,CAAC;IAED,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,OAAO;gBACL,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM,AAAC,GAAc,OAAZ;YACT,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF;QACA,UAAU;YACR,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,UAAU;YACZ;QACF;QACA,OAAO;YACL,YAAY;YACZ,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,WAAW;YACX,WAAW;YACX,WAAW;gBACT;oBACE,MAAM,CAAC;oBACP,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;aACD;QACH;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,eAAe;QACjB;QACA,QAAQ;YACN,SAAS;YACT,WAAW;gBACT,OAAO;YACT;YACA,gBAAgB;gBACd,OAAO;YACT;QACF;QACA,aAAa;YACX,QAAQ;gBACN,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,YAAY;oBACV,SAAS;gBACX;YACF;QACF;QACA,QAAQ;QACR,SAAS;YACP,SAAS;QACX;QACA,WAAW;YACT,SAAS;YACT,SAAS;gBACP,eAAe;oBACb,cAAc;oBACd,OAAO;wBACL,MAAM;wBACN,QAAQ;oBACV;gBACF;YACF;QACF;QACA,SAAS;YACP,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,OAAO;gBACL,OAAO;YACT;YACA,SAAS;YACT,WAAW;gBACT,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,IAAI;gBACpC,MAAM,kBAAkB,IAAI,CAAC,CAAC;gBAC9B,MAAM,eAAe,IAAI,CAAC,CAAC;gBAE3B,kDAAkD;gBAClD,MAAM,cAAc,iBAAiB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,KAAK;gBAE7D,IAAI,CAAC,aAAa;oBAChB,OAAO,AAAC,MACE,OADG,aAAY,iCACQ,OAAvB,iBAAgB,SAAoB,OAAb,cAAa;gBAChD;gBAEA,IAAI,iBAAiB,AAAC,8GAEuB,OAD2B,aAAY,+DACnB,OAApB,iBAAgB,MAAiB,OAAb,cAAa;gBAI9E,MAAM,YAAY;oBAAC;oBAAO;oBAAQ;oBAAQ;oBAAS;oBAAO;iBAAO;gBACjE,UAAU,OAAO,CAAC,CAAA;oBAChB,MAAM,QAAQ,YAAY,SAAS,CAAC,SAA+C;oBACnF,MAAM,oBAAoB,aAAa,OAAO;oBAC9C,kBAAkB,AAAC,8BACf,OAD4C,oBAAoB,uCAAuC,mBAAkB,oBAC5G,OAAb,UAAS,MAAU,OAAN,OAAM;gBAEzB;gBAEA,kBAAkB;gBAClB,OAAO;YACT;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,mMAAe;YACd,YAAY,sJAAU;YACtB,SAAS;;;;;;;;;;;AAIjB;GAhOM;KAAA;uCAkOS", "debugId": null}}]}