{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ColumnChart.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ColumnChart.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ColumnChart.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ColumnChart.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ColumnChart.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ColumnChart.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/app/page.tsx"], "sourcesContent": ["import ColumnChart from \"@/components/ColumnChart\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-6\">\n        {/* Header */}\n        <div className=\"text-center space-y-2\">\n          <h1 className=\"text-4xl font-bold text-foreground\">\n            Univer 2025 Dashboard\n          </h1>\n          <p className=\"text-muted-foreground text-lg\">\n            Modern adatvizualizáció Highcharts-szal\n          </p>\n        </div>\n\n        {/* Chart Card */}\n        <Card className=\"w-full\">\n          <CardHeader>\n            <CardTitle className=\"text-2xl\">Havi <PERSON></CardTitle>\n            <CardDescription>\n              Oszlop diagram a havi értékek megjelenítésére\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <ColumnC<PERSON>\n              title=\"Havi <PERSON>\"\n              data={[\n                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 29.9 },\n                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 71.5 },\n                { name: '<PERSON><PERSON><PERSON><PERSON>', y: 106.4 },\n                { name: '<PERSON>p<PERSON><PERSON>', y: 129.2 },\n                { name: '<PERSON><PERSON>jus', y: 144.0 },\n                { name: '<PERSON><PERSON><PERSON>', y: 176.0 },\n                { name: '<PERSON><PERSON><PERSON>', y: 135.6 },\n                { name: 'Augusztus', y: 148.5 }\n              ]}\n            />\n          </CardContent>\n        </Card>\n\n        {/* Stats Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                Összes Érték\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">841.1</div>\n              <p className=\"text-xs text-muted-foreground\">\n                +12.5% az előző hónaphoz képest\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                Átlag\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">105.1</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Havi átlagérték\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <CardTitle className=\"text-sm font-medium text-muted-foreground\">\n                Legmagasabb\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">176.0</div>\n              <p className=\"text-xs text-muted-foreground\">\n                Június hónapban\n              </p>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC,wIAAI;oBAAC,WAAU;;sCACd,8OAAC,8IAAU;;8CACT,8OAAC,6IAAS;oCAAC,WAAU;8CAAW;;;;;;8CAChC,8OAAC,mJAAe;8CAAC;;;;;;;;;;;;sCAInB,8OAAC,+IAAW;sCACV,cAAA,8OAAC,4IAAW;gCACV,OAAM;gCACN,MAAM;oCACJ;wCAAE,MAAM;wCAAU,GAAG;oCAAK;oCAC1B;wCAAE,MAAM;wCAAW,GAAG;oCAAK;oCAC3B;wCAAE,MAAM;wCAAW,GAAG;oCAAM;oCAC5B;wCAAE,MAAM;wCAAW,GAAG;oCAAM;oCAC5B;wCAAE,MAAM;wCAAS,GAAG;oCAAM;oCAC1B;wCAAE,MAAM;wCAAU,GAAG;oCAAM;oCAC3B;wCAAE,MAAM;wCAAU,GAAG;oCAAM;oCAC3B;wCAAE,MAAM;wCAAa,GAAG;oCAAM;iCAC/B;;;;;;;;;;;;;;;;;8BAMP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wIAAI;;8CACH,8OAAC,8IAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,6IAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,+IAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,wIAAI;;8CACH,8OAAC,8IAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,6IAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,+IAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC,wIAAI;;8CACH,8OAAC,8IAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC,6IAAS;wCAAC,WAAU;kDAA4C;;;;;;;;;;;8CAInE,8OAAC,+IAAW;;sDACV,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3D", "debugId": null}}]}