(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/highcharts/modules/exporting.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_highcharts_modules_exporting_0129b23c.js",
  "static/chunks/node_modules_highcharts_modules_exporting_f063e976.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/highcharts/modules/exporting.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/highcharts/modules/export-data.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_highcharts_modules_export-data_e5f5e94b.js",
  "static/chunks/node_modules_highcharts_modules_export-data_f063e976.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/highcharts/modules/export-data.js [app-client] (ecmascript)");
    });
});
}),
]);