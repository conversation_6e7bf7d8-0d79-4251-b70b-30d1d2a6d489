'use client';

import React from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

interface ColumnChartProps {
  title?: string;
  data?: Array<{ name: string; y: number }>;
}

const ColumnChart: React.FC<ColumnChartProps> = ({ 
  title = "Oszlop Diagram", 
  data = [
    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 29.9 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 71.5 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 106.4 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 129.2 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 144.0 },
    { name: '<PERSON><PERSON><PERSON>', y: 176.0 }
  ]
}) => {
  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'var(--font-geist-sans)'
      }
    },
    title: {
      text: title,
      style: {
        color: '#ffffff',
        fontSize: '20px',
        fontWeight: '600'
      }
    },
    xAxis: {
      type: 'category',
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      lineColor: '#3f3f46',
      tickColor: '#3f3f46'
    },
    yAxis: {
      title: {
        text: 'Értékek',
        style: {
          color: '#a1a1aa'
        }
      },
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      gridLineColor: '#3f3f46'
    },
    legend: {
      enabled: false
    },
    plotOptions: {
      column: {
        borderWidth: 0,
        borderRadius: 4,
        color: {
          linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
          stops: [
            [0, '#8b5cf6'],
            [1, '#6366f1']
          ]
        },
        dataLabels: {
          enabled: true,
          style: {
            color: '#ffffff',
            textOutline: 'none'
          }
        }
      }
    },
    series: [{
      type: 'column',
      name: 'Adatok',
      data: data
    }],
    credits: {
      enabled: false
    },
    tooltip: {
      backgroundColor: '#27272a',
      borderColor: '#3f3f46',
      style: {
        color: '#ffffff'
      }
    }
  };

  return (
    <div className="w-full h-96">
      <HighchartsReact
        highcharts={Highcharts}
        options={options}
      />
    </div>
  );
};

export default ColumnChart;
